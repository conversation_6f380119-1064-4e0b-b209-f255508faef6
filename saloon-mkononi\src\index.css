@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Poppins:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    background-color: #111111;
    color: #FFFFFF;
    font-family: 'Poppins', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    min-height: 100vh;
    overflow-x: hidden;
  }
}

@layer components {
  .font-serif-display {
    font-family: 'Playfair Display', serif;
  }

  /* Custom letter spacing to match the design */
  .tracking-widest-plus {
    letter-spacing: 0.25em;
  }

  /* Custom styles for service numbers */
  .service-number {
    position: absolute;
    left: -1.5rem;
    bottom: -1.5rem;
    font-size: 5rem;
    line-height: 1;
    color: rgba(255, 255, 255, 0.15);
    z-index: -1;
    pointer-events: none;
  }

  /* Enhanced button styles */
  .btn-primary {
    @apply border border-white/70 px-6 py-2.5 rounded-full hover:bg-white hover:text-black transition-all duration-300 text-[10px] uppercase tracking-wider;
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
  }

  /* Enhanced link styles */
  .nav-link {
    @apply hover:text-gray-300 transition-colors duration-200;
  }

  .nav-link:hover {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  }

  /* Image hover effects */
  .image-hover {
    @apply transition-transform duration-500 ease-out;
  }

  .image-hover:hover {
    transform: scale(1.02);
  }

  /* Section spacing */
  .section-padding {
    @apply py-16 lg:py-24;
  }

  /* Container with consistent padding */
  .container-padding {
    @apply container mx-auto px-6 lg:px-12;
  }

  /* Animation classes */
  .animate-fade-in {
    animation: fadeIn 1s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scroll animations */
@media (prefers-reduced-motion: no-preference) {
  .scroll-animate {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
  }

  .scroll-animate.in-view {
    opacity: 1;
    transform: translateY(0);
  }
}
